import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Initialize Redis for rate limiting
let redis: Redis | null = null;
let isRedisConfigured = false;

try {
  if (process.env.UPSTASH_REDIS_REST_URL &&
      process.env.UPSTASH_REDIS_REST_TOKEN &&
      process.env.UPSTASH_REDIS_REST_URL !== 'https://example.upstash.io') {
    redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    });
    isRedisConfigured = true;
  }
} catch (error) {
  console.warn('Redis not configured, using in-memory fallback for development');
}

// Rate limit configurations
let rateLimits: { [key: string]: Ratelimit } = {};

if (isRedisConfigured && redis) {
  rateLimits = {
    chat: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(10, '5 m'), // 10 requests per 5 minutes
      analytics: true,
    }),
    admin: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(5, '10 m'), // 5 requests per 10 minutes
      analytics: true,
    }),
    upload: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(2, '1 h'), // 2 uploads per hour
      analytics: true,
    }),
    urlProcess: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(3, '30 m'), // 3 URLs per 30 minutes
      analytics: true,
    }),
    api: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(30, '1 m'), // 30 requests per minute
      analytics: true,
    }),
  };
}

// In-memory fallback for development
const inMemoryLimits = new Map<string, { count: number; resetTime: number }>();

function checkInMemoryRateLimit(key: string, limit: number, windowMs: number): { success: boolean; limit: number; reset: number; remaining: number } {
  const now = Date.now();
  const stored = inMemoryLimits.get(key);

  if (!stored || now > stored.resetTime) {
    inMemoryLimits.set(key, { count: 1, resetTime: now + windowMs });
    return { success: true, limit, reset: now + windowMs, remaining: limit - 1 };
  }

  if (stored.count >= limit) {
    return { success: false, limit, reset: stored.resetTime, remaining: 0 };
  }

  stored.count++;
  return { success: true, limit, reset: stored.resetTime, remaining: limit - stored.count };
}

function checkInMemoryRateLimitStatus(key: string, limit: number, windowMs: number): { success: boolean; limit: number; reset: number; remaining: number } {
  const now = Date.now();
  const stored = inMemoryLimits.get(key);

  if (!stored || now > stored.resetTime) {
    return { success: true, limit, reset: now + windowMs, remaining: limit };
  }

  return { success: stored.count < limit, limit, reset: stored.resetTime, remaining: limit - stored.count };
}

// Rate limit configurations
function getRateLimitConfig(type: string): { limit: number; windowMs: number } {
  switch (type) {
    case 'chat':
      return { limit: 10, windowMs: 5 * 60 * 1000 }; // 10 requests per 5 minutes
    case 'admin':
      return { limit: 5, windowMs: 10 * 60 * 1000 }; // 5 requests per 10 minutes
    case 'upload':
      return { limit: 2, windowMs: 60 * 60 * 1000 }; // 2 uploads per hour
    case 'urlProcess':
      return { limit: 3, windowMs: 30 * 60 * 1000 }; // 3 URLs per 30 minutes
    case 'api':
    default:
      return { limit: 30, windowMs: 60 * 1000 }; // 30 requests per minute
  }
}

// Get client IP address
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

// Check rate limit and consume a token
export async function checkRateLimit(request: Request, rateLimitType: string): Promise<{ success: boolean; limit: number; reset: number; remaining: number }> {
  const ip = getClientIP(request);

  if (isRedisConfigured && rateLimits[rateLimitType]) {
    // Use Redis-based rate limiting
    const result = await rateLimits[rateLimitType].limit(ip);
    return result;
  } else {
    // Use in-memory fallback for development
    const config = getRateLimitConfig(rateLimitType);
    const key = `${rateLimitType}:${ip}`;
    return checkInMemoryRateLimit(key, config.limit, config.windowMs);
  }
}

// Check rate limit status - NOTE: This WILL consume a token for accuracy
// This is a trade-off: we get 100% accurate status but it costs 1 token per check
export async function checkRateLimitStatus(request: Request, rateLimitType: string): Promise<{ success: boolean; limit: number; reset: number; remaining: number }> {
  const ip = getClientIP(request);

  if (isRedisConfigured && rateLimits[rateLimitType]) {
    // Use the actual rate limiter - this will consume a token but gives us 100% accurate results
    const result = await rateLimits[rateLimitType].limit(ip);

    // Log that we consumed a token for status check (for debugging)
    if (process.env.NODE_ENV === 'development') {
      console.log(`Rate limit status check consumed 1 token for ${rateLimitType}. Remaining: ${result.remaining}/${result.limit}`);
    }

    return {
      success: result.success,
      limit: result.limit,
      reset: result.reset,
      remaining: result.remaining
    };
  } else {
    // Use in-memory fallback for development - this doesn't consume a token
    const config = getRateLimitConfig(rateLimitType);
    const key = `${rateLimitType}:${ip}`;
    return checkInMemoryRateLimitStatus(key, config.limit, config.windowMs);
  }
}

export { isRedisConfigured, rateLimits, getRateLimitConfig, getClientIP };

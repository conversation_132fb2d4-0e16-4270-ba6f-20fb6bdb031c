import { NextRequest, NextResponse } from 'next/server';
import { checkRateLimitStatus, isRedisConfigured, rateLimits, getClientIP } from '@/lib/rate-limiting';

export async function GET(request: NextRequest) {
  try {
    const rateLimitType = 'chat'; // We're checking chat rate limits

    // Use the shared rate limiting utility to check status without consuming a token
    const result = await checkRateLimitStatus(request, rateLimitType);
    const { success, limit, reset, remaining } = result;

    // Log the status check for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log(`Rate limit status check: ${remaining}/${limit} remaining, success: ${success}`);
    }

    return NextResponse.json({
      success,
      limit,
      remaining,
      resetTime: reset,
      type: rateLimitType
    });

  } catch (error) {
    console.error('Rate limit status check error:', error);
    // Return conservative default values if check fails
    return NextResponse.json({
      success: false,
      limit: 10,
      remaining: 0,
      resetTime: Date.now() + (5 * 60 * 1000),
      type: 'chat'
    });
  }
}
